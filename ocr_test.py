import requests
import base64

# 加载图片并编码为 base64
with open("/Users/<USER>/Downloads/BGSNRT.pdf", "rb") as img_file:
    b64_image = base64.b64encode(img_file.read()).decode()

url = "http://localhost:11434/api/generate"

headers = {
    "Content-Type": "application/json"          # 一般 JSON 请求需要这个
}

# 请求 Ollama API
response = requests.post("http://************:11434/api/generate",headers, json={
    "model": "OCRFlux-3B:Q8_0",
    "prompt": "请识别以下图像中的文字",
    "images": [b64_image]
})

print(f"状态码: {response.status_code}")
print(f"响应头: {response.headers}")

# 先检查状态码
if response.status_code == 200:
    try:
        response_data = response.json()
        print(f"完整响应: {response_data}")

        # 检查响应结构
        if "response" in response_data:
            print(f"响应内容: {response_data['response']}")
        else:
            print("响应中没有'response'字段")
            print(f"可用字段: {list(response_data.keys())}")
    except Exception as e:
        print(f"JSON解析失败: {e}")
        print(f"原始响应文本: {response.text}")
else:
    print(f"请求失败，状态码: {response.status_code}")
    print(f"错误响应: {response.text}")