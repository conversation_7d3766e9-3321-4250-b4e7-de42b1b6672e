import requests
import base64
import os

# 文件路径 - 请替换为实际的图像文件路径
file_path = "/Users/<USER>/app/upload/wechat_2025-07-30_131736_952.png"  # 请使用 .jpg, .png, .jpeg 等图像格式

# 检查文件是否存在
if not os.path.exists(file_path):
    print(f"错误: 文件不存在: {file_path}")
    exit(1)

print(f"正在处理文件: {file_path}")
print(f"文件大小: {os.path.getsize(file_path)} bytes")

# 加载文件并编码为 base64
with open(file_path, "rb") as img_file:
    file_data = img_file.read()
    b64_image = base64.b64encode(file_data).decode()

print(f"Base64编码长度: {len(b64_image)}")
print(f"Base64前50个字符: {b64_image[:50]}...")

url = "http://localhost:11434/api/generate"

headers = {
    "Content-Type": "application/json"
}

# 构建请求数据
request_data = {
    "model": "myaniu/OCRFlux-3B:Q8_0",
    "prompt": "请识别以下图像中的文字，并合并成到一起",
    "images": [b64_image]
}

print(f"请求URL: http://************:11434/api/generate")
print(f"请求头: {headers}")
print(f"请求数据键: {list(request_data.keys())}")
print(f"模型: {request_data['model']}")
print(f"提示词: {request_data['prompt']}")
print(f"图片数量: {len(request_data['images'])}")
print("-" * 50)

# 请求 Ollama API
response = requests.post("http://************:11434/api/generate", headers=headers, json=request_data)

print(f"状态码: {response.status_code}")
print(f"响应头: {response.headers}")

# 先检查状态码
if response.status_code == 200:
    try:
        response_data = response.json()
        print(f"完整响应: {response_data}")

        # 检查响应结构
        if "response" in response_data:
            print(f"响应内容: {response_data['response']}")
        else:
            print("响应中没有'response'字段")
            print(f"可用字段: {list(response_data.keys())}")
    except Exception as e:
        print(f"JSON解析失败: {e}")
        print(f"原始响应文本: {response.text}")
else:
    print(f"请求失败，状态码: {response.status_code}")
    print(f"错误响应: {response.text}")