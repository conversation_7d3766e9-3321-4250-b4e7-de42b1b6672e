import requests
import base64

# 加载图片并编码为 base64
with open("/Users/<USER>/Downloads/BGSNRT.pdf", "rb") as img_file:
    b64_image = base64.b64encode(img_file.read()).decode()

url = "http://localhost:11434/api/generate"

headers = {
    "Content-Type": "application/json"          # 一般 JSON 请求需要这个
}

# 请求 Ollama API
response = requests.post("http://************:11434/api/generate",headers, json={
    "model": "OCRFlux-3B:Q8_0",
    "prompt": "请识别以下图像中的文字",
    "images": [b64_image]
})

print(response)

print(response.json()["response"])