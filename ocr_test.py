import requests
import base64
import os
import json

# 文件路径 - 请替换为实际的图像文件路径
file_path = "/Users/<USER>/Downloads/wechat_2025-07-30_132357_458.png"  # 请使用 .jpg, .png, .jpeg 等图像格式

# 检查文件是否存在
if not os.path.exists(file_path):
    print(f"错误: 文件不存在: {file_path}")
    exit(1)

print(f"正在处理文件: {file_path}")
print(f"文件大小: {os.path.getsize(file_path)} bytes")

# 加载文件并编码为 base64
with open(file_path, "rb") as img_file:
    file_data = img_file.read()
    b64_image = base64.b64encode(file_data).decode()

print(f"Base64编码长度: {len(b64_image)}")
print(f"Base64前50个字符: {b64_image[:50]}...")

url = "http://localhost:11434/api/generate"

headers = {
    "Content-Type": "application/json"
}

# 构建请求数据
request_data = {
    "model": "myaniu/OCRFlux-3B:Q8_0",
    "prompt": "请识别以下图像中的文字",
    "images": [b64_image]
}

print(f"请求URL: http://************:11434/api/generate")
print(f"请求头: {headers}")
print(f"请求数据键: {list(request_data.keys())}")
print(f"模型: {request_data['model']}")
print(f"提示词: {request_data['prompt']}")
print(f"图片数量: {len(request_data['images'])}")
print("-" * 50)

# 请求 Ollama API
response = requests.post("http://************:11434/api/generate", headers=headers, json=request_data)

print(f"状态码: {response.status_code}")
print(f"响应头: {response.headers}")

# 先检查状态码
if response.status_code == 200:
    try:
        # Ollama API返回的是流式响应，每行是一个JSON对象
        response_text = response.text.strip()
        print(f"原始响应文本:\n{response_text}")
        print("-" * 50)

        # 分割每行并解析JSON
        lines = response_text.split('\n')
        full_response = ""

        for i, line in enumerate(lines):
            if line.strip():  # 跳过空行
                try:
                    line_data = json.loads(line)
                    print(f"第{i+1}行JSON: {line_data}")

                    # 检查是否有response字段
                    if "response" in line_data:
                        full_response += line_data["response"]

                    # 检查是否有错误
                    if "error" in line_data:
                        print(f"错误: {line_data['error']}")

                except json.JSONDecodeError as e:
                    print(f"第{i+1}行JSON解析失败: {e}")
                    print(f"原始行内容: {line}")

        print("-" * 50)
        print("完整识别结果:")
        print(full_response)

    except Exception as e:
        print(f"处理响应失败: {e}")
        print(f"原始响应文本: {response.text}")
else:
    print(f"请求失败，状态码: {response.status_code}")
    print(f"错误响应: {response.text}")